/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { useSchemaTree, useAuth, SchemaTreeMapEntry, SchemaWithLifeCycleDetail } from '@glidesystems/caching-store';
import { useEffect, useMemo } from 'react';
import get from 'lodash/get';
import isNil from 'lodash/isNil';
import orderBy from 'lodash/orderBy';
import { buildInitialValue } from '../utils/formBuilder';
import { buildValidationSchema } from '@glidesystems/styleguide';
import includes from 'lodash/includes';
import filter from 'lodash/filter';
import isEmpty from 'lodash/isEmpty';
import { AttributeType, RelationType, User } from '@glidesystems/api';
import uniqBy from 'lodash/uniqBy';

export const isValidAttributeToRender = (attribute: any) =>
    attribute && attribute.visible && isEmpty(attribute.identifier) && attribute.mutable;

const isRelationRequired = (r: RelationType) => (r.visible || r.name === 'OWNED_BY' ) && r.required && r.direction === 'Outgoing';

const getRequireRelations = (schemaDetail: SchemaWithLifeCycleDetail) => {
    return filter(schemaDetail?.relationTypes, isRelationRequired) || [];
};

const useRequiredRelations = (
    schemaDetail: SchemaWithLifeCycleDetail,
    masterSchemaDetail?: SchemaWithLifeCycleDetail
) => {
    const revisionRelations = useMemo(() => {
        return uniqBy(
            [
                ...getRequireRelations(schemaDetail),
                ...(masterSchemaDetail ? getRequireRelations(masterSchemaDetail) : []),
            ],
            'name'
        );
    }, [schemaDetail, masterSchemaDetail]);
    return revisionRelations;
};

const useInitialValues = (schemaDetail: SchemaWithLifeCycleDetail, masterSchemaDetail: SchemaWithLifeCycleDetail) => {
    const [initialValues, validationSchema] = useMemo(() => {
        if (!schemaDetail) return [null, null];

        const revisionAttributes = Object.values(get(schemaDetail, 'attributes', {}));
        const masterAttributes = Object.values(get(masterSchemaDetail, 'attributes', {}));
        const allAttributes = [...revisionAttributes, ...masterAttributes].filter((a) => isValidAttributeToRender(a));
        const defaultLifecycle = schemaDetail.lifecycles?.find((lifecycle) => lifecycle?.lifeCycle?.default);
        const defaultState = defaultLifecycle
            ? Object.values(defaultLifecycle.states).find((state) => includes(state['canCreate'], 'Any'))
            : null;
        const requiredRelations = getRequireRelations(schemaDetail);
        const requiredMasterRelations = getRequireRelations(masterSchemaDetail);
        return [
            {
                ...buildInitialValue(allAttributes, null),
                owner: null,
                lifeCycle: {
                    id: get(defaultLifecycle, 'lifeCycle.id'),
                    startState: get(defaultState, 'id'),
                },
                lock: false,
                ...requiredRelations.reduce(
                    (prev, curr) => ({
                        ...prev,
                        [`relation-${curr.name}`]: null,
                    }),
                    {}
                ),
                ...requiredMasterRelations.reduce(
                    (prev, curr) => ({
                        ...prev,
                        [`relation-${curr.name}`]: null,
                    }),
                    {}
                ),
            },
            buildValidationSchema([
                ...allAttributes,
                {
                    name: 'owner',
                    type: AttributeType.STRING,
                    displayName: 'Owner',
                    nullable: false,
                    hyperlink: false,
                    constraint: null,
                    isMulti: false,
                    unitOfMeasure: null,
                },
                ...requiredRelations.map((relation) => ({
                    name: `relation-${relation.name}`,
                    type: AttributeType.ASYNC_SELECT,
                    displayName: relation.displayName,
                    nullable: false,
                    isMulti: !relation.singleRelation,
                    hyperlink: false,
                    constraint: null,
                    unitOfMeasure: null,
                })),
                ...requiredMasterRelations.map((relation) => ({
                    name: `relation-${relation.name}`,
                    type: AttributeType.ASYNC_SELECT,
                    displayName: relation.displayName,
                    nullable: false,
                    isMulti: !relation.singleRelation,
                    hyperlink: false,
                    constraint: null,
                    unitOfMeasure: null,
                })),
            ]),
        ];
    }, [schemaDetail, masterSchemaDetail]);
    return [initialValues, validationSchema];
};

export { useInitialValues, useRequiredRelations, getRequireRelations };
