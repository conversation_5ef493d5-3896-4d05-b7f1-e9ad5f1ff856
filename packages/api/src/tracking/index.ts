/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import fetch, { Method } from '../services/fetch';
import { entityUrls, SYSTEM_ENTITY_TYPE } from '../glidesystems-api';
import { buildInQuery } from '../utils/queryHelper';
import groupBy from 'lodash/groupBy';
import { CUSTOM_EVENTS } from '../constants/events';

const BASE_URL = `${process.env.BASE_URL}/tracking/tracking`;
const DELAY_TRACKING_EVENT = 2500;
const DELAY_NOTIFY_EVENT = 1000;

const sendViewedEntityEvent = () => {
    setTimeout(() => {
        window.dispatchEvent(new CustomEvent(CUSTOM_EVENTS.ENTITY_VIEWED));
    }, DELAY_NOTIFY_EVENT);
};
const trackingService = {
    async trackViewedEntity(entityType: string, entityId: string, signal?: AbortSignal) {
        try {
            const request: any = {
                url: `${BASE_URL}/user-defined`,
                method: Method.POST,
                data: {
                    type: CUSTOM_EVENTS.ENTITY_VIEWED,
                    data: {
                        id: entityId,
                        type: entityType,
                    },
                },
                signal,
                skipToast: true,
            };
            setTimeout(async () => {
                await fetch(request);
                // Notify so any place use recenlty viewed entities should refresh the data
                sendViewedEntityEvent();
            }, DELAY_TRACKING_EVENT);
        } catch (err) {
            console.error('Failed to track viewed entity due to error', err);
        }
    },
    async getRecentlyViewed(actorId, limit, offset) {
        const req: any = {
            url: `${BASE_URL}/user-defined`,
            method: Method.GET,
            qs: {
                eventType: CUSTOM_EVENTS.ENTITY_VIEWED,
                actorId,
                limit,
                offset,
            },
        };
        try {
            const {
                data: { data: events, pageInfo },
            } = await fetch(req);
            let ids = [];
            events.forEach((event) => {
                const id = event.payload.id;
                if (id) {
                    ids.push(id);
                }
            });

            const entitiesReq: any = {
                ...entityUrls.getListEntity,
                params: { entityType: SYSTEM_ENTITY_TYPE.SYS_ROOT },
                qs: {
                    query: JSON.stringify(buildInQuery('id', ids)),
                    limit,
                    fields: ['revision', 'isMaster', 'primaryFileExtension', 'isBookmarked'],
                },
            };
            const {
                data: { data: entities },
            } = await fetch(entitiesReq);

            const groupedEntities = groupBy(entities, 'id');
            let results = [];
            events.forEach((event) => {
                const entity = groupedEntities[event.payload.id];
                if (entity && entity.length > 0) {
                    results.push({
                        ...event,
                        entity: entity[0],
                    });
                }
            });
            return {
                data: results,
                pageInfo: {
                    nextOffset: Math.min(offset + limit, pageInfo.total),
                    lastRow: offset + limit >= pageInfo.total,
                },
            };
        } catch (err) {
            return {
                data: null,
            };
        } finally {
        }
    },
    getActivity: {
        method: Method.GET,
        url: `${BASE_URL}/activity`,
    },
};
export default trackingService;
