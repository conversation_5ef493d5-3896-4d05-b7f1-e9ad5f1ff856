{"version": 3, "file": "glidesystems-api.d.ts", "sourceRoot": "", "sources": ["../src/glidesystems-api.tsx"], "names": [], "mappings": "AAaA,OAAO,EAAE,OAAO,IAAI,MAAM,EAAE,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAE,OAAO,IAAI,UAAU,EAAE,MAAM,kCAAkC,CAAC;AACzE,OAAO,EACH,OAAO,IAAI,SAAS,EACpB,cAAc,EACd,uBAAuB,EACvB,gBAAgB,GACnB,MAAM,iCAAiC,CAAC;AACzC,OAAO,EAAE,OAAO,IAAI,UAAU,EAAE,MAAM,kCAAkC,CAAC;AACzE,OAAO,EAAE,KAAK,cAAc,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AACrF,OAAO,EAAE,OAAO,IAAI,aAAa,EAAE,MAAM,oCAAoC,CAAC;AAC9E,OAAO,EAAE,OAAO,IAAI,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACnE,OAAO,EAAE,OAAO,IAAI,UAAU,EAAE,MAAM,UAAU,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AACrC,OAAO,EAAE,OAAO,IAAI,aAAa,EAAE,MAAM,aAAa,CAAC;AACvD,OAAO,EAAE,OAAO,IAAI,aAAa,EAAE,MAAM,aAAa,CAAC;AACvD,OAAO,EAAE,OAAO,IAAI,iBAAiB,EAAE,MAAM,sCAAsC,CAAC;AACpF,OAAO,EAAE,KAAK,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACxD,cAAc,kCAAkC,CAAC;AACjD,OAAO,EAAE,OAAO,IAAI,eAAe,EAAE,MAAM,uCAAuC,CAAC;AACnF,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,sBAAsB,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AACxH,OAAO,EAAE,OAAO,IAAI,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AACrF,OAAO,EAAE,OAAO,IAAI,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,6BAA6B,CAAC;AAC1F,OAAO,EAAE,eAAe,EAAE,MAAM,0CAA0C,CAAC;AAC3E,OAAO,EAAE,OAAO,IAAI,SAAS,EAAE,cAAc,EAAE,MAAM,oCAAoC,CAAC;AAC1F,OAAO,EAAE,OAAO,IAAI,qBAAqB,EAAE,MAAM,gDAAgD,CAAC;AAClG,OAAO,EAAE,OAAO,IAAI,cAAc,EAAE,MAAM,4CAA4C,CAAC;AACvF,OAAO,EAAE,OAAO,IAAI,oBAAoB,EAAE,MAAM,oDAAoD,CAAC;AACrG,OAAO,EAAE,OAAO,IAAI,wBAAwB,EAAE,MAAM,iDAAiD,CAAC;AACtG,OAAO,EAAE,OAAO,IAAI,QAAQ,EAAE,KAAK,UAAU,EAAE,MAAM,QAAQ,CAAC;AAC9D,wBAAgB,iBAAiB,SAAK;AACtC,OAAO,EAAE,OAAO,IAAI,aAAa,EAAE,MAAM,iCAAiC,CAAC;AAC3E,OAAO,EAAE,OAAO,IAAI,eAAe,EAAE,MAAM,gBAAgB,CAAC;AAC5D,OAAO,EAAE,OAAO,IAAI,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAC1E,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,kBAAkB,CAAC;AACtF,OAAO,EAAE,OAAO,IAAI,UAAU,EAAE,MAAM,kBAAkB,CAAC;AACzD,OAAO,EAAE,OAAO,IAAI,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AACzE,OAAO,EAAE,OAAO,IAAI,IAAI,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,OAAO,IAAI,KAAK,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,EAAE,OAAO,IAAI,WAAW,EAAE,MAAM,+BAA+B,CAAC;AACvE,OAAO,EAAE,OAAO,IAAI,QAAQ,EAAE,MAAM,SAAS,CAAC;AAC9C,eAAO,MAAM,aAAa,QAA4B,CAAC;AACvD,OAAO,EAAE,OAAO,IAAI,qBAAqB,EAAE,MAAM,yCAAyC,CAAC;AAC3F,OAAO,EAAE,OAAO,IAAI,eAAe,EAAE,MAAM,YAAY,CAAC;AACxD,OAAO,EAAE,OAAO,IAAI,UAAU,EAAE,MAAM,UAAU,CAAC;AACjD,OAAO,EAAE,OAAO,IAAI,aAAa,EAAE,MAAM,aAAa,CAAC;AACvD,eAAO,MAAM,oBAAoB,QAAmC,CAAC;AACrE,OAAO,EACH,kBAAkB,EAClB,oBAAoB,EACpB,qBAAqB,EACrB,eAAe,EACf,eAAe,EACf,yBAAyB,EACzB,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,qBAAqB,EACrB,eAAe,EACf,KAAK,EACL,WAAW,EACX,sBAAsB,EACtB,qBAAqB,EACrB,uBAAuB,EACvB,0BAA0B,EAC1B,YAAY,EACZ,kBAAkB,EAClB,iCAAiC,EACjC,KAAK,cAAc,EACnB,kBAAkB,EAClB,oBAAoB,GACvB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACH,OAAO,IAAI,gBAAgB,EAC3B,YAAY,EACZ,qBAAqB,EACrB,iBAAiB,EACjB,KAAK,gBAAgB,EACrB,KAAK,oBAAoB,GAC5B,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAC9D,OAAO,EAAE,OAAO,IAAI,UAAU,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,EAAE,OAAO,IAAI,eAAe,EAAE,gBAAgB,EAAE,kCAAkC,EAAE,MAAM,UAAU,CAAC;AAC5G,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,cAAc,oBAAoB,CAAC;AACnC,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,cAAc,yBAAyB,CAAC;AACxC,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,OAAO,IAAI,WAAW,EAAE,mBAAmB,EAAE,MAAM,WAAW,CAAC;AACxE,eAAO,MAAM,iBAAiB,QAAuC,CAAC;AACtE,OAAO,EAAE,OAAO,IAAI,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AAChE,cAAc,mCAAmC,CAAC;AAClD,cAAc,sBAAsB,CAAC;AACrC,eAAO,MAAM,yBAAyB,OAAO,CAAC;AAC9C,eAAO,MAAM,wBAAwB,KAAK,CAAC;AAC3C,OAAO,EAAE,KAAK,aAAa,EAAE,MAAM,OAAO,CAAC;AAC3C,cAAc,wBAAwB,CAAC;AACvC,cAAc,UAAU,CAAC"}