{"version": 3, "file": "batchRequest.d.ts", "sourceRoot": "", "sources": ["../../src/utils/batchRequest.ts"], "names": [], "mappings": "AAYA,OAAc,EAAgB,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAMhE,OAAO,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC;AAItC,UAAU,WAAW;IACjB,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC/B,IAAI,EAAE,GAAG,CAAC;IACV,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,EAAE,MAAM,CAAC;CAClB;AAED,UAAU,0BAA2B,SAAQ,WAAW;IACpD,WAAW,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;CACxC;AAED,MAAM,MAAM,gBAAgB,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAEnE,MAAM,WAAW,oBAAoB;IACjC,OAAO,EAAE,OAAO,CAAC;IACjB,IAAI,CAAC,EAAE,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;IACjC,KAAK,CAAC,EAAE,GAAG,CAAC;CACf;AAED,UAAU,YAAY;IAClB,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IACjC,eAAe,CAAC,EAAE,MAAM,CAAC;CAC5B;AAED,UAAU,wBAAyB,SAAQ,YAAY;IACnD,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,IAAI,CAAC;IAC9C,MAAM,EAAE,WAAW,CAAC;CACvB;AAED,UAAU,kBAAkB;IACxB,QAAQ,EAAE,oBAAoB,CAAC;IAC/B,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC/B,SAAS,CAAC,EAAE,MAAM,IAAI,CAAC;IACvB,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC;IACtB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,aAAa,CAAC,EAAE,MAAM,CAAC;CAC1B;AAED,QAAA,MAAM,kBAAkB,wBAA+B,MAAM,QAAQ,gBAAgB,EAAE,KAAG,gBAAgB,EAiBzG,CAAC;AAEF,eAAO,MAAM,YAAY,4CAKtB,YAAY,KAAG,OAAO,CAAC,oBAAoB,CAkC7C,CAAC;AAEF,eAAO,MAAM,iBAAiB,sEAO3B,wBAAwB,KAAG,OAAO,CAAC,oBAAoB,CAyCzD,CAAC;AAEF,eAAO,MAAM,qBAAqB,2FAO/B,kBAAkB,KAAG,IAyBvB,CAAC;AAEF,eAAe,kBAAkB,CAAC"}